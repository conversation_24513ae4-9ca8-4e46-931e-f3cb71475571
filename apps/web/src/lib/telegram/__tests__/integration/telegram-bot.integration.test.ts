/**
 * Integration tests for TelegramBotService
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { TelegramBotService } from "../../../telegram-bot";

// Mock TelegramBot
vi.mock("node-telegram-bot-api", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getMe: vi.fn().mockResolvedValue({
        id: 123456789,
        username: "test_bot",
        first_name: "Test Bot",
        is_bot: true,
      }),
      setWebHook: vi.fn().mockResolvedValue(true),
      deleteWebHook: vi.fn().mockResolvedValue(true),
      getWebHookInfo: vi.fn().mockResolvedValue({
        url: "https://example.com/webhook",
        has_custom_certificate: false,
        pending_update_count: 0,
      }),
      startPolling: vi.fn().mockResolvedValue(undefined),
      stopPolling: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      on: vi.fn(),
      sendMessage: vi.fn().mockResolvedValue({
        message_id: 1,
        date: Date.now(),
        chat: { id: 123, type: "private" },
        text: "Response message",
      }),
      answerCallbackQuery: vi.fn().mockResolvedValue(true),
    })),
  };
});

// Mock external dependencies
vi.mock("../../telegram/services/telegram-session.service", () => ({
  TelegramSessionService: vi.fn().mockImplementation(() => ({
    storeSessionData: vi.fn(),
    getSessionData: vi.fn(),
    addMessageToHistory: vi.fn(),
  })),
}));

vi.mock("../../telegram/services/telegram-user.service", () => ({
  TelegramUserService: vi.fn().mockImplementation(() => ({
    getOrCreateTelegramUser: vi.fn().mockResolvedValue({
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123",
      createdAt: new Date(),
      lastActiveAt: new Date(),
    }),
    getTelegramUser: vi.fn(),
    getUserStats: vi.fn(),
  })),
}));

vi.mock("../../telegram/services/telegram-security.service", () => ({
  TelegramSecurityService: vi.fn().mockImplementation(() => ({
    checkMessageSecurity: vi.fn().mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    }),
    checkRateLimit: vi.fn().mockResolvedValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    }),
  })),
}));

vi.mock("../../telegram/utils/telegram-logger", () => ({
  TelegramLogger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    logCommand: vi.fn(),
    logMessageProcessing: vi.fn(),
    logCallback: vi.fn(),
  })),
}));

describe("TelegramBotService Integration", () => {
  let botService: TelegramBotService;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(async () => {
    if (botService && botService.isReady()) {
      await botService.shutdown();
    }
  });

  it("should initialize with polling configuration", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();

    expect(botService.isReady()).toBe(true);

    const stats = await botService.getStats();
    expect(stats.botInfo.username).toBe("test_bot");
    expect(stats.isPolling).toBe(true);
    expect(stats.architecture).toBe("modular");
  });

  it("should initialize with webhook configuration", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      webhookUrl: "https://example.com/webhook",
      enablePolling: false,
    });

    await botService.initialize();

    expect(botService.isReady()).toBe(true);

    const stats = await botService.getStats();
    expect(stats.botInfo.username).toBe("test_bot");
    expect(stats.isPolling).toBe(false);
    expect(stats.webhookInfo?.url).toBe("https://example.com/webhook");
  });

  it("should process start command update", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();

    const update = {
      update_id: 1,
      message: {
        message_id: 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/start",
      },
    };

    await botService.processUpdate(update);

    // Verify that the bot sent a response
    const bot = botService.getBot();
    expect(bot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("Welcome"),
      expect.any(Object)
    );
  });

  it("should process Twitter URL update", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();

    const update = {
      update_id: 2,
      message: {
        message_id: 2,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "https://twitter.com/user/status/1234567890",
      },
    };

    // Mock Twitter URL validation and processing
    vi.doMock("../../telegram/utils/telegram-validator", () => ({
      validateTwitterUrl: vi.fn().mockReturnValue({
        isValid: true,
        error: null,
      }),
    }));

    vi.doMock("../../rate-limiting", () => ({
      checkTelegramUserRateLimit: vi.fn().mockResolvedValue({
        allowed: true,
        remaining: 10,
        resetTime: Date.now() + 60000,
      }),
    }));

    await botService.processUpdate(update);

    // Should process the Twitter URL (exact verification depends on mocked services)
    const bot = botService.getBot();
    expect(bot.sendMessage).toHaveBeenCalled();
  });

  it("should process callback query update", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();

    const update = {
      update_id: 3,
      callback_query: {
        id: "callback-1",
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        message: {
          message_id: 1,
          date: Math.floor(Date.now() / 1000),
          chat: { id: 123, type: "private" as const },
        },
        data: "show_help",
      },
    };

    await botService.processUpdate(update);

    const bot = botService.getBot();
    expect(bot.answerCallbackQuery).toHaveBeenCalledWith("callback-1");
  });

  it("should handle router statistics", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();

    const stats = await botService.getStats();

    expect(stats.router).toBeDefined();
    expect(stats.router.commandHandlers).toBeGreaterThan(0);
    expect(stats.router.commands).toContain("start");
    expect(stats.router.commands).toContain("help");
    expect(stats.router.commands).toContain("create");
  });

  it("should handle initialization errors", async () => {
    // Mock bot.getMe to throw an error
    const TelegramBot = (await import("node-telegram-bot-api")).default;
    const mockBot = new TelegramBot("test-token");
    vi.mocked(mockBot.getMe).mockRejectedValue(new Error("Network error"));

    botService = new TelegramBotService({
      token: "invalid-token",
      enablePolling: true,
    });

    await expect(botService.initialize()).rejects.toThrow();
    expect(botService.isReady()).toBe(false);
  });

  it("should shutdown gracefully", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    await botService.initialize();
    expect(botService.isReady()).toBe(true);

    await botService.shutdown();
    expect(botService.isReady()).toBe(false);

    const bot = botService.getBot();
    expect(bot.close).toHaveBeenCalled();
  });

  it("should handle webhook updates without initialization", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      webhookUrl: "https://example.com/webhook",
    });

    // Don't initialize, just process update
    const update = {
      update_id: 4,
      message: {
        message_id: 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
        },
        text: "Hello",
      },
    };

    // Should not throw error even if not initialized
    await expect(botService.processUpdate(update)).resolves.not.toThrow();
  });

  it("should maintain backward compatibility", async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });

    // Test legacy methods still work
    expect(botService.getBot()).toBeDefined();
    expect(typeof botService.processUpdate).toBe("function");
    expect(typeof botService.initialize).toBe("function");
  });
});
