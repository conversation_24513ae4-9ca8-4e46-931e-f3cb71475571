/**
 * Performance tests for TelegramBotService
 * Ensures new architecture doesn't introduce performance regressions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { TelegramBotService } from "../../telegram-bot";

// Mock dependencies for performance testing
vi.mock("node-telegram-bot-api", () => ({
  default: vi.fn().mockImplementation(() => ({
    getMe: vi.fn().mockResolvedValue({
      id: 123456789,
      username: "test_bot",
      first_name: "Test Bot",
      is_bot: true,
    }),
    setWebHook: vi.fn().mockResolvedValue(true),
    startPolling: vi.fn().mockResolvedValue(undefined),
    stopPolling: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    sendMessage: vi.fn().mockResolvedValue({
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      text: "Response",
    }),
    answerCallbackQuery: vi.fn().mockResolvedValue(true),
  })),
}));

// Mock services with minimal overhead
vi.mock("../../telegram/services/telegram-session.service", () => ({
  TelegramSessionService: vi.fn().mockImplementation(() => ({
    storeSessionData: vi.fn().mockResolvedValue(undefined),
    getSessionData: vi.fn().mockResolvedValue(null),
    addMessageToHistory: vi.fn().mockResolvedValue(undefined),
  })),
}));

vi.mock("../../telegram/services/telegram-user.service", () => ({
  TelegramUserService: vi.fn().mockImplementation(() => ({
    getOrCreateTelegramUser: vi.fn().mockResolvedValue({
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123",
      createdAt: new Date(),
      lastActiveAt: new Date(),
    }),
  })),
}));

vi.mock("../../telegram/services/telegram-security.service", () => ({
  TelegramSecurityService: vi.fn().mockImplementation(() => ({
    checkMessageSecurity: vi.fn().mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    }),
    checkRateLimit: vi.fn().mockResolvedValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    }),
  })),
}));

describe("TelegramBotService Performance", () => {
  let botService: TelegramBotService;

  beforeEach(async () => {
    botService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });
    await botService.initialize();
  });

  afterEach(async () => {
    if (botService && botService.isReady()) {
      await botService.shutdown();
    }
  });

  it("should initialize quickly", async () => {
    const startTime = performance.now();
    
    const newBotService = new TelegramBotService({
      token: "test-token",
      enablePolling: true,
    });
    
    await newBotService.initialize();
    
    const endTime = performance.now();
    const initTime = endTime - startTime;
    
    // Initialization should complete within 100ms
    expect(initTime).toBeLessThan(100);
    
    await newBotService.shutdown();
  });

  it("should process commands efficiently", async () => {
    const commandUpdate = {
      update_id: 1,
      message: {
        message_id: 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "/start",
      },
    };

    const startTime = performance.now();
    
    await botService.processUpdate(commandUpdate);
    
    const endTime = performance.now();
    const processTime = endTime - startTime;
    
    // Command processing should complete within 50ms
    expect(processTime).toBeLessThan(50);
  });

  it("should handle high volume of updates", async () => {
    const updates = Array.from({ length: 100 }, (_, i) => ({
      update_id: i + 1,
      message: {
        message_id: i + 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: `/help`,
      },
    }));

    const startTime = performance.now();
    
    // Process all updates concurrently
    await Promise.all(
      updates.map(update => botService.processUpdate(update))
    );
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTimePerUpdate = totalTime / updates.length;
    
    // Average processing time should be under 10ms per update
    expect(avgTimePerUpdate).toBeLessThan(10);
    
    // Total time should be under 1 second for 100 updates
    expect(totalTime).toBeLessThan(1000);
  });

  it("should handle callback queries efficiently", async () => {
    const callbackUpdate = {
      update_id: 2,
      callback_query: {
        id: "callback-1",
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        message: {
          message_id: 1,
          date: Math.floor(Date.now() / 1000),
          chat: { id: 123, type: "private" as const },
        },
        data: "show_help",
      },
    };

    const startTime = performance.now();
    
    await botService.processUpdate(callbackUpdate);
    
    const endTime = performance.now();
    const processTime = endTime - startTime;
    
    // Callback processing should complete within 30ms
    expect(processTime).toBeLessThan(30);
  });

  it("should route messages efficiently", async () => {
    const messageUpdate = {
      update_id: 3,
      message: {
        message_id: 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: "Hello, how are you?",
      },
    };

    const startTime = performance.now();
    
    await botService.processUpdate(messageUpdate);
    
    const endTime = performance.now();
    const processTime = endTime - startTime;
    
    // Message routing should complete within 40ms
    expect(processTime).toBeLessThan(40);
  });

  it("should handle mixed update types efficiently", async () => {
    const mixedUpdates = [
      {
        update_id: 1,
        message: {
          message_id: 1,
          date: Math.floor(Date.now() / 1000),
          chat: { id: 123, type: "private" as const },
          from: { id: 456, is_bot: false, first_name: "John" },
          text: "/start",
        },
      },
      {
        update_id: 2,
        callback_query: {
          id: "callback-1",
          from: { id: 456, is_bot: false, first_name: "John" },
          message: {
            message_id: 1,
            date: Math.floor(Date.now() / 1000),
            chat: { id: 123, type: "private" as const },
          },
          data: "show_help",
        },
      },
      {
        update_id: 3,
        message: {
          message_id: 2,
          date: Math.floor(Date.now() / 1000),
          chat: { id: 123, type: "private" as const },
          from: { id: 456, is_bot: false, first_name: "John" },
          text: "General message",
        },
      },
    ];

    const startTime = performance.now();
    
    for (const update of mixedUpdates) {
      await botService.processUpdate(update);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTimePerUpdate = totalTime / mixedUpdates.length;
    
    // Average processing time should be under 20ms per update
    expect(avgTimePerUpdate).toBeLessThan(20);
  });

  it("should maintain performance under memory pressure", async () => {
    // Create a large number of updates to simulate memory pressure
    const largeUpdates = Array.from({ length: 1000 }, (_, i) => ({
      update_id: i + 1,
      message: {
        message_id: i + 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: `Message ${i + 1} with some content to increase memory usage`,
      },
    }));

    const startTime = performance.now();
    
    // Process in batches to simulate real-world usage
    const batchSize = 50;
    for (let i = 0; i < largeUpdates.length; i += batchSize) {
      const batch = largeUpdates.slice(i, i + batchSize);
      await Promise.all(
        batch.map(update => botService.processUpdate(update))
      );
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTimePerUpdate = totalTime / largeUpdates.length;
    
    // Should maintain reasonable performance even with large volumes
    expect(avgTimePerUpdate).toBeLessThan(15);
    
    // Total time should be reasonable for 1000 updates
    expect(totalTime).toBeLessThan(15000); // 15 seconds max
  });

  it("should have minimal memory footprint", async () => {
    const initialMemory = process.memoryUsage();
    
    // Process a reasonable number of updates
    const updates = Array.from({ length: 200 }, (_, i) => ({
      update_id: i + 1,
      message: {
        message_id: i + 1,
        date: Math.floor(Date.now() / 1000),
        chat: { id: 123, type: "private" as const },
        from: {
          id: 456,
          is_bot: false,
          first_name: "John",
          username: "john_doe",
        },
        text: `/help`,
      },
    }));

    await Promise.all(
      updates.map(update => botService.processUpdate(update))
    );
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
    
    // Memory increase should be reasonable (less than 10MB for 200 updates)
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
  });

  it("should shutdown quickly", async () => {
    const startTime = performance.now();
    
    await botService.shutdown();
    
    const endTime = performance.now();
    const shutdownTime = endTime - startTime;
    
    // Shutdown should complete within 100ms
    expect(shutdownTime).toBeLessThan(100);
    
    // Don't shutdown again in afterEach
    botService = null as any;
  });
});
