/**
 * Tests for TwitterUrlProcessor
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import type TelegramBot from "node-telegram-bot-api";
import { TwitterUrlProcessor } from "../../processors/twitter-url.processor";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

// Mock dependencies
const mockBot = {
  sendMessage: vi.fn(),
} as any;

const mockUserService = {
  getOrCreateTelegramUser: vi.fn(),
} as any;

const mockSessionService = {
  storeSessionData: vi.fn(),
} as any;

const mockSecurityService = {
  checkMessageSecurity: vi.fn(),
} as any;

const mockLogger = {
  logMessageProcessing: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
} as any;

const mockContext: TelegramHandlerContext = {
  bot: mockBot,
  userService: mockUserService,
  sessionService: mockSessionService,
  securityService: mockSecurityService,
  logger: mockLogger,
  config: {
    token: "test-token",
    enablePolling: true,
  },
};

// Mock external dependencies
vi.mock("../../utils/telegram-validator", () => ({
  validateTwitterUrl: vi.fn(),
}));

vi.mock("../../../telegram-benji-agent", () => ({
  getTelegramBenjiForUser: vi.fn(),
}));

vi.mock("../../../rate-limiting", () => ({
  checkTelegramUserRateLimit: vi.fn(),
}));

describe("TwitterUrlProcessor", () => {
  let processor: TwitterUrlProcessor;

  beforeEach(() => {
    processor = new TwitterUrlProcessor();
    vi.clearAllMocks();
  });

  it("should have correct priority and description", () => {
    expect(processor.getPriority()).toBe(90);
    expect(processor.getDescription()).toBe("Process Twitter/X URLs and generate AI responses");
  });

  it("should handle Twitter URLs", () => {
    const twitterMessage: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      text: "https://twitter.com/user/status/**********",
    };

    const xMessage: TelegramBot.Message = {
      message_id: 2,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      text: "https://x.com/user/status/**********",
    };

    const nonTwitterMessage: TelegramBot.Message = {
      message_id: 3,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      text: "https://example.com/some-page",
    };

    expect(processor.canHandle(twitterMessage)).toBe(true);
    expect(processor.canHandle(xMessage)).toBe(true);
    expect(processor.canHandle(nonTwitterMessage)).toBe(false);
  });

  it("should not handle messages without text", () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      // No text property
    };

    expect(processor.canHandle(message)).toBe(false);
  });

  it("should process Twitter URL successfully", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "https://twitter.com/user/status/**********",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123",
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    const mockBenji = {
      generateTelegramQuickReply: vi.fn().mockResolvedValue({
        textStream: (async function* () {
          yield "This is a great tweet! ";
          yield "Thanks for sharing.";
        })(),
        recordUsage: vi.fn(),
      }),
    };

    // Mock imports
    const { validateTwitterUrl } = await import("../../utils/telegram-validator");
    const { getTelegramBenjiForUser } = await import("../../../telegram-benji-agent");
    const { checkTelegramUserRateLimit } = await import("../../../rate-limiting");

    vi.mocked(validateTwitterUrl).mockReturnValue({
      isValid: true,
      error: null,
    });

    vi.mocked(checkTelegramUserRateLimit).mockResolvedValue({
      allowed: true,
      remaining: 10,
      resetTime: Date.now() + 60000,
    });

    vi.mocked(getTelegramBenjiForUser).mockResolvedValue(mockBenji);

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);
    mockSecurityService.checkMessageSecurity.mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    });

    // Mock tweet content extraction
    const originalExtractTweetContent = processor['extractTweetContent'];
    processor['extractTweetContent'] = vi.fn().mockResolvedValue("Original tweet content here");

    await processor.process(message, mockContext);

    expect(mockLogger.logMessageProcessing).toHaveBeenCalledWith(
      "twitter_url",
      "456",
      "123",
      true
    );

    expect(mockUserService.getOrCreateTelegramUser).toHaveBeenCalledWith(message.from);
    expect(mockBenji.generateTelegramQuickReply).toHaveBeenCalledWith(
      "Original tweet content here",
      "https://twitter.com/user/status/**********"
    );

    expect(mockSessionService.storeSessionData).toHaveBeenCalledWith(
      "tg-user-1",
      expect.objectContaining({
        twitterUrl: "https://twitter.com/user/status/**********",
        responseText: "This is a great tweet! Thanks for sharing.",
        type: "twitter_url",
      })
    );

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("🐦 *AI Reply Generated*"),
      expect.objectContaining({
        parse_mode: "Markdown",
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🔄 Regenerate",
                callback_data: "regenerate:tg-user-1",
              }),
            ]),
          ]),
        }),
      })
    );
  });

  it("should handle invalid Twitter URL", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "https://twitter.com/invalid-url",
    };

    const { validateTwitterUrl } = await import("../../utils/telegram-validator");
    vi.mocked(validateTwitterUrl).mockReturnValue({
      isValid: false,
      error: "Invalid Twitter URL format",
    });

    mockSecurityService.checkMessageSecurity.mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    });

    await processor.process(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      "❌ Invalid Twitter URL: Invalid Twitter URL format"
    );

    expect(mockUserService.getOrCreateTelegramUser).not.toHaveBeenCalled();
  });

  it("should handle unlinked user", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "https://twitter.com/user/status/**********",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: null, // Not linked
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    const { validateTwitterUrl } = await import("../../utils/telegram-validator");
    vi.mocked(validateTwitterUrl).mockReturnValue({
      isValid: true,
      error: null,
    });

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);
    mockSecurityService.checkMessageSecurity.mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    });

    await processor.process(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("🔗 *Account Required for Twitter Processing*"),
      expect.objectContaining({
        parse_mode: "Markdown",
        reply_markup: expect.objectContaining({
          inline_keyboard: expect.arrayContaining([
            expect.arrayContaining([
              expect.objectContaining({
                text: "🔗 Link Account",
                callback_data: "link_account",
              }),
            ]),
          ]),
        }),
      })
    );
  });

  it("should handle rate limit exceeded", async () => {
    const message: TelegramBot.Message = {
      message_id: 1,
      date: Date.now(),
      chat: { id: 123, type: "private" },
      from: {
        id: 456,
        is_bot: false,
        first_name: "John",
        username: "john_doe",
      },
      text: "https://twitter.com/user/status/**********",
    };

    const mockTelegramUser = {
      id: "tg-user-1",
      telegramId: "456",
      userId: "user-123",
      createdAt: new Date(),
      lastActiveAt: new Date(),
    };

    const { validateTwitterUrl } = await import("../../utils/telegram-validator");
    const { checkTelegramUserRateLimit } = await import("../../../rate-limiting");

    vi.mocked(validateTwitterUrl).mockReturnValue({
      isValid: true,
      error: null,
    });

    vi.mocked(checkTelegramUserRateLimit).mockResolvedValue({
      allowed: false,
      remaining: 0,
      resetTime: Date.now() + 300000, // 5 minutes
    });

    mockUserService.getOrCreateTelegramUser.mockResolvedValue(mockTelegramUser);
    mockSecurityService.checkMessageSecurity.mockResolvedValue({
      allowed: true,
      reason: null,
      securityLevel: "safe",
      issues: [],
    });

    await processor.process(message, mockContext);

    expect(mockBot.sendMessage).toHaveBeenCalledWith(
      123,
      expect.stringContaining("⚠️ Rate limit exceeded. Please wait 5 minutes")
    );
  });
});
