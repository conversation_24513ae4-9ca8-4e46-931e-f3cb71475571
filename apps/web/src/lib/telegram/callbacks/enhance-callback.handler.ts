/**
 * Enhance Callback Handler
 * Handles enhance button clicks with o3 model integration for both /create and Twitter URL responses
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCallbackHandler } from "./base/callback-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { getTelegramBenjiForUser } from "../../telegram-benji-agent";

/**
 * <PERSON><PERSON> for enhance button callbacks
 * Uses OpenAI o3 model for highest quality enhanced responses
 */
export class EnhanceCallbackHandler extends BaseCallbackHandler {
  constructor() {
    super("enhance", "Handle enhance button clicks with o3 model integration");
  }

  async handle(query: TelegramBot.CallbackQuery, context: TelegramHandlerContext): Promise<void> {
    if (!await this.validateQuery(query, context)) {
      return;
    }

    const chatId = query.message!.chat.id;
    const telegramUserId = this.extractPayload(query.data!);

    context.logger.logCallback(
      "enhance",
      query.from.id.toString(),
      chatId.toString(),
      true
    );

    try {
      // Answer the callback query to remove loading state
      await this.answerCallback(query, context, "Enhancing with o3 model...");

      // Get session data
      const sessionData = await context.sessionService.getSessionData(telegramUserId);

      context.logger.debug("Enhance handler - Retrieved session data", {
        telegramUserId,
        metadata: {
          hasSessionData: !!sessionData,
          sessionType: sessionData?.type || "unknown",
          hasPrompt: !!sessionData?.prompt,
          hasTwitterUrl: !!sessionData?.twitterUrl,
          prompt: sessionData?.prompt?.substring(0, 50) + "..." || "N/A",
          twitterUrl: sessionData?.twitterUrl || "N/A",
        },
      });

      if (!sessionData) {
        await context.bot.sendMessage(
          chatId,
          "❌ Session expired. Please generate new content or try again."
        );
        return;
      }

      // Get user information
      const dbTelegramUser = await context.userService.getTelegramUser(telegramUserId);
      if (!dbTelegramUser?.userId) {
        await context.bot.sendMessage(
          chatId,
          "🔗 Account required for enhanced generation. Please link your account first."
        );
        return;
      }

      // Handle different session types
      if (sessionData.type === "create_command") {
        await this.handleCreateCommandEnhance(chatId, sessionData, dbTelegramUser.userId, context);
      } else if (sessionData.type === "twitter_url") {
        await this.handleTwitterUrlEnhance(chatId, sessionData, dbTelegramUser.userId, context);
      } else {
        // Fallback: try to determine type from available data
        if (sessionData.twitterUrl) {
          await this.handleTwitterUrlEnhance(chatId, sessionData, dbTelegramUser.userId, context);
        } else if (sessionData.prompt) {
          await this.handleCreateCommandEnhance(chatId, sessionData, dbTelegramUser.userId, context);
        } else {
          await context.bot.sendMessage(
            chatId,
            "❌ Cannot enhance: No original content found. Please create new content first."
          );
        }
      }

      context.logger.info("Enhance callback handled successfully", {
        chatId: chatId.toString(),
        userId: query.from.id.toString(),
        metadata: {
          sessionType: sessionData.type,
          hasPrompt: !!sessionData.prompt,
          hasTwitterUrl: !!sessionData.twitterUrl,
        },
      });
    } catch (error) {
      context.logger.error("Error in enhance callback handler", {
        chatId: chatId.toString(),
        telegramUserId,
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error, context);
    }
  }

  /**
   * Handle enhance for /create command generated posts
   */
  private async handleCreateCommandEnhance(
    chatId: number,
    sessionData: any,
    userId: string,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.prompt) {
      await context.bot.sendMessage(
        chatId,
        "❌ No original prompt found. Please use /create to generate a new post."
      );
      return;
    }

    await context.bot.sendMessage(
      chatId,
      "✨ Generating enhanced post with o3 model..."
    );

    context.logger.debug("Enhancing create command with o3 model", {
      metadata: {
        prompt: sessionData.prompt.substring(0, 100) + "...",
        promptLength: sessionData.prompt.length,
        userId,
      },
    });

    try {
      // Get Telegram Benji agent for enhanced generation
      const benji = await getTelegramBenjiForUser(
        userId,
        sessionData.telegramUserId || "unknown",
        chatId.toString()
      );

      // Generate enhanced post using o3 model
      const result = await benji.generateTelegramEnhancedResponse(sessionData.prompt, {
        telegramUserId: sessionData.telegramUserId || "unknown",
        telegramChatId: chatId.toString(),
      });

      // Process the streaming result
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      if (responseText.trim()) {
        // Store enhanced response in session
        const enhancedSessionData = {
          ...sessionData,
          responseText,
          timestamp: Date.now(),
          enhanced: true,
        };
        await context.sessionService.storeSessionData(sessionData.telegramUserId || "unknown", enhancedSessionData);

        await context.bot.sendMessage(
          chatId,
          `✨ *Enhanced Post (o3 Model):*\n\n${responseText}`,
          {
            parse_mode: "Markdown",
            reply_markup: {
              inline_keyboard: [
                [
                  { text: "🔄 Regenerate", callback_data: `regenerate:${sessionData.telegramUserId || "unknown"}` },
                  { text: "📋 Copy", callback_data: `copy:${sessionData.telegramUserId || "unknown"}` },
                ],
                [
                  { text: "🎨 Create New", callback_data: "quick_create" },
                  { text: "📚 Help", callback_data: "show_help" },
                ],
              ],
            },
          }
        );

        context.logger.debug("Enhanced create command completed", {
          metadata: {
            originalLength: sessionData.responseText?.length || 0,
            enhancedLength: responseText.length,
            userId,
          },
        });
      } else {
        await context.bot.sendMessage(
          chatId,
          "❌ Sorry, I couldn't generate an enhanced post right now. Please try again later."
        );
      }
    } catch (enhanceError) {
      context.logger.error("Error generating enhanced post", {
        metadata: { 
          error: enhanceError instanceof Error ? enhanceError.message : String(enhanceError),
          prompt: sessionData.prompt.substring(0, 50),
          userId,
        },
      });

      await context.bot.sendMessage(
        chatId,
        "❌ Error generating enhanced post. Please try again."
      );
    }
  }

  /**
   * Handle enhance for Twitter URL replies
   */
  private async handleTwitterUrlEnhance(
    chatId: number,
    sessionData: any,
    userId: string,
    context: TelegramHandlerContext
  ): Promise<void> {
    if (!sessionData.twitterUrl) {
      await context.bot.sendMessage(
        chatId,
        "❌ No original Twitter URL found. Please send the Twitter URL again."
      );
      return;
    }

    await context.bot.sendMessage(
      chatId,
      "✨ Generating enhanced reply with o3 model..."
    );

    context.logger.debug("Enhancing Twitter URL response with o3 model", {
      metadata: {
        twitterUrl: sessionData.twitterUrl,
        userId,
      },
    });

    try {
      // Get Telegram Benji agent for enhanced generation
      const benji = await getTelegramBenjiForUser(
        userId,
        sessionData.telegramUserId || "unknown",
        chatId.toString()
      );

      // For Twitter URL enhancement, we need the original tweet content
      // This is a placeholder - in a full implementation, you'd extract the tweet content again
      const enhancementPrompt = `Generate an enhanced, more sophisticated reply to this Twitter URL: ${sessionData.twitterUrl}`;

      // Generate enhanced response using o3 model
      const result = await benji.generateTelegramEnhancedResponse(enhancementPrompt, {
        telegramUserId: sessionData.telegramUserId || "unknown",
        telegramChatId: chatId.toString(),
        twitterUrl: sessionData.twitterUrl,
      });

      // Process the streaming result
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      if (responseText.trim()) {
        // Store enhanced response in session
        const enhancedSessionData = {
          ...sessionData,
          responseText,
          timestamp: Date.now(),
          enhanced: true,
        };
        await context.sessionService.storeSessionData(sessionData.telegramUserId || "unknown", enhancedSessionData);

        await context.bot.sendMessage(
          chatId,
          `✨ *Enhanced Reply (o3 Model):*\n\n${responseText}`,
          {
            parse_mode: "Markdown",
            reply_markup: {
              inline_keyboard: [
                [
                  { text: "🔄 Regenerate", callback_data: `regenerate:${sessionData.telegramUserId || "unknown"}` },
                  { text: "🚀 Send Reply", callback_data: `copy:${sessionData.telegramUserId || "unknown"}` },
                ],
                [
                  { text: "🐦 View Original", url: sessionData.twitterUrl },
                  { text: "📚 Help", callback_data: "show_help" },
                ],
              ],
            },
          }
        );

        context.logger.debug("Enhanced Twitter URL response completed", {
          metadata: {
            originalLength: sessionData.responseText?.length || 0,
            enhancedLength: responseText.length,
            twitterUrl: sessionData.twitterUrl,
            userId,
          },
        });
      } else {
        await context.bot.sendMessage(
          chatId,
          "❌ Sorry, I couldn't generate an enhanced reply right now. Please try again later."
        );
      }
    } catch (enhanceError) {
      context.logger.error("Error generating enhanced Twitter reply", {
        metadata: { 
          error: enhanceError instanceof Error ? enhanceError.message : String(enhanceError),
          twitterUrl: sessionData.twitterUrl,
          userId,
        },
      });

      await context.bot.sendMessage(
        chatId,
        "❌ Error generating enhanced reply. Please try again."
      );
    }
  }
}
