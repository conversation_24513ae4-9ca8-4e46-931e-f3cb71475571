/**
 * Create Command Handler
 * Handles the /create command for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCommandHandler } from "./base/command-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { getTelegramBenjiForUser } from "../../telegram-benji-agent";

/**
 * <PERSON><PERSON> for the /create command
 * Generates original social media content using AI
 */
export class CreateCommandHandler extends BaseCommandHandler {
  constructor() {
    super("create", "Generate original social media posts with AI");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      await context.bot.sendMessage(
        chatId,
        "❌ Unable to identify user. Please try again."
      );
      return;
    }

    context.logger.logCommand("create", telegramUser.id.toString(), chatId.toString(), true);

    try {
      // Extract the prompt from the command
      const commandText = message.text || "";
      const prompt = commandText.replace("/create", "").trim();

      if (!prompt) {
        await this.sendUsageInstructions(chatId, context);
        return;
      }

      // Get or create user in database
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountRequiredMessage(chatId, context);
        return;
      }

      // Send "generating" message
      const generatingMsg = await context.bot.sendMessage(
        chatId,
        "🎨 *Generating your post...*\n\nThis may take a few moments.",
        { parse_mode: "Markdown" }
      );

      // Get Telegram Benji agent for this user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Generate the post using AI
      const result = await benji.generateTelegramPost(prompt, {
        telegramUserId: dbTelegramUser.id,
        telegramChatId: chatId.toString(),
      });

      // Convert streaming result to text
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      context.logger.debug("Generated response text", {
        telegramUserId: dbTelegramUser.id,
        metadata: {
          responseLength: responseText.length,
          responsePreview: responseText.substring(0, 100) + "...",
          hasContent: !!responseText.trim(),
        },
      });

      // Record usage
      await result.recordUsage();

      // Delete the "generating" message
      try {
        await context.bot.deleteMessage(chatId, generatingMsg.message_id);
      } catch (deleteError) {
        context.logger.warn("Failed to delete generating message", {
          telegramUserId: dbTelegramUser.id,
          metadata: { error: deleteError },
        });
      }

      if (responseText.trim()) {
        // Store the response text in session for callback handling
        const createSessionData = {
          responseText,
          prompt,
          timestamp: Date.now(),
          type: "create_command" as const,
        };

        context.logger.debug("Storing session data", {
          telegramUserId: dbTelegramUser.id,
          metadata: {
            responseTextLength: responseText.length,
            responseTextPreview: responseText.substring(0, 50) + "...",
            prompt: prompt.substring(0, 50) + "...",
          },
        });

        await context.sessionService.storeSessionData(dbTelegramUser.id, createSessionData);

        // Send the generated post with action buttons
        await context.bot.sendMessage(
          chatId,
          `🎨 *Generated Post:*\n\n${responseText}`,
          {
            parse_mode: "Markdown",
            reply_markup: {
              inline_keyboard: [
                [
                  {
                    text: "🔄 Regenerate",
                    callback_data: `regenerate:${dbTelegramUser.id}`,
                  },
                  { text: "✨ Enhance", callback_data: `enhance:${dbTelegramUser.id}` },
                ],
                [{ text: "📋 Copy", callback_data: `copy:${dbTelegramUser.id}` }],
              ],
            },
          }
        );

        context.logger.info("Create command completed successfully", {
          chatId: chatId.toString(),
          userId: telegramUser.id.toString(),
          metadata: { 
            command: "/create", 
            promptLength: prompt.length,
            responseLength: responseText.length,
          },
        });
      } else {
        await context.bot.sendMessage(
          chatId,
          "❌ Sorry, I couldn't generate a post right now. Please try again with a different prompt."
        );

        context.logger.warn("Empty response generated", {
          telegramUserId: dbTelegramUser.id,
          metadata: { prompt: prompt.substring(0, 100) },
        });
      }
    } catch (error) {
      context.logger.error("Error in /create command", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error, context);
    }
  }

  /**
   * Send usage instructions for the /create command
   */
  private async sendUsageInstructions(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const usageMessage = `🎨 *Create AI Post*

Please provide a prompt for your post:

\`/create Your prompt here\`

*Examples:*
• \`/create A motivational post about learning new skills\`
• \`/create Share insights about remote work productivity\`
• \`/create A funny observation about coffee addiction\`
• \`/create Tips for staying focused while working from home\`

*Tips for better results:*
• Be specific about the tone (professional, casual, funny)
• Mention the target audience if relevant
• Include the main message or insight you want to share
• Specify the platform if you have a preference

Ready to create something amazing? 🚀`;

    const keyboard = [
      [
        { text: "💡 Example: Motivation", callback_data: "create_example_motivation" },
        { text: "💼 Example: Business", callback_data: "create_example_business" },
      ],
      [
        { text: "😄 Example: Funny", callback_data: "create_example_funny" },
        { text: "📚 Example: Educational", callback_data: "create_example_educational" },
      ],
      [{ text: "📖 More Help", callback_data: "show_help" }],
    ];

    await context.bot.sendMessage(chatId, usageMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }

  /**
   * Send account required message
   */
  private async sendAccountRequiredMessage(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const accountMessage = `🔗 *Account Required*

To use the /create command, you need to link your BuddyChip account first.

*Why link your account?*
• Access to premium AI models
• Higher usage limits
• Conversation memory
• Enhanced responses
• Priority support

Use /start to get started!`;

    const keyboard = [
      [{ text: "🔗 Link Account", callback_data: "link_account" }],
      [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
      [{ text: "🎯 Try Demo", callback_data: "demo_mode" }],
    ];

    await context.bot.sendMessage(chatId, accountMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }
}
