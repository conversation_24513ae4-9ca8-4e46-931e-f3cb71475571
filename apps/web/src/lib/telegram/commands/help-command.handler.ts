/**
 * Help Command Handler
 * Handles the /help command for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCommandHandler } from "./base/command-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";

/**
 * <PERSON><PERSON> for the /help command
 * Provides comprehensive help information and usage instructions
 */
export class HelpCommandHandler extends BaseCommandHandler {
  constructor() {
    super("help", "Show detailed help information and usage instructions");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      await context.bot.sendMessage(
        chatId,
        "❌ Unable to identify user. Please try again."
      );
      return;
    }

    context.logger.logCommand("help", telegramUser.id.toString(), chatId.toString(), true);

    try {
      const helpMessage = `
📚 *BuddyChip AI Help*

*🤖 What I Do:*
I'm your AI assistant for social media content creation and Twitter engagement.

*🐦 Twitter Integration:*
• Send me any Twitter URL
• I'll analyze the tweet and generate a smart reply
• Perfect for engaging with your audience

*🎨 Content Creation:*
• Use \`/create [your prompt]\` to generate original posts
• Be specific about tone, style, and topic
• Great for social media content planning

*🖼️ Image Generation:*
• Use \`/image [detailed prompt]\` for AI-generated images
• Include style, mood, colors, and composition details
• Perfect for visual content creation

*⚙️ Commands:*
• \`/start\` - Welcome message and setup
• \`/help\` - This help message
• \`/settings\` - Account settings and linking
• \`/status\` - Check usage limits and account info
• \`/create [prompt]\` - Generate social media posts
• \`/image [prompt]\` - Generate AI images

*🔗 Account Linking:*
• Link your BuddyChip account for full access
• Unlinked accounts have limited functionality
• Use /settings to manage your account

*Tips:*
• Be specific in your requests
• Use detailed prompts for images
• Check /status for usage limits
• Link your account for full access

Need more help? Contact support through the BuddyChip dashboard.
    `;

      const keyboard = [
        [
          { text: "🎨 Try Create", callback_data: "quick_create" },
          { text: "🔗 Link Account", callback_data: "link_account" },
        ],
        [
          { text: "⚙️ Settings", callback_data: "show_settings" },
          { text: "📊 Status", callback_data: "show_status" },
        ],
        [
          { text: "🌐 Dashboard", url: "https://buddychip.app/dashboard" },
        ],
      ];

      await context.bot.sendMessage(chatId, helpMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });

      context.logger.info("Help message sent successfully", {
        chatId: chatId.toString(),
        userId: telegramUser.id.toString(),
        metadata: { command: "/help" },
      });
    } catch (error) {
      context.logger.error("Error in /help command", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error, context);
    }
  }
}
