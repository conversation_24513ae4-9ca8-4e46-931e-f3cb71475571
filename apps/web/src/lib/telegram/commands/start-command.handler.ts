/**
 * Start Command Handler
 * Handles the /start command for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCommandHandler } from "./base/command-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";

/**
 * <PERSON><PERSON> for the /start command
 * Provides welcome message and account linking options
 */
export class Start<PERSON>ommandHandler extends BaseCommandHandler {
  constructor() {
    super("start", "Start using the bot and get welcome information");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      await context.bot.sendMessage(
        chatId,
        "❌ Unable to identify user. Please try again."
      );
      return;
    }

    context.logger.logCommand("start", telegramUser.id.toString(), chatId.toString(), true);

    try {
      // Get or create user in database
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      // Create personalized welcome message
      const firstName = telegramUser.first_name || "there";
      const isLinked = !!dbTelegramUser.userId;

      const welcomeMessage = `🤖 *Welcome to BuddyChip AI, ${firstName}!*

I'm your AI assistant for social media content creation and Twitter engagement.

*What I can do:*
🐦 Generate smart replies to Twitter posts
🎨 Create original social media content
🖼️ Generate AI images with detailed prompts
🔍 Search the web for information
💬 Have intelligent conversations

*Quick Start:*
${
  isLinked
    ? "✅ Your account is linked! You have full access to all features."
    : "🔗 Link your BuddyChip account for full access to premium features."
}

*Available Commands:*
/help - Show detailed help
/create [prompt] - Generate social media posts
/settings - Manage your account
/status - Check usage and limits

*Twitter Integration:*
Just send me any Twitter URL and I'll generate a smart reply for you!

Ready to get started? Try sending me a Twitter URL or use /create to make your first post! 🚀`;

      // Create inline keyboard based on account status
      const keyboard = isLinked
        ? [
            [
              { text: "🎨 Create Post", callback_data: "quick_create" },
              { text: "📊 Dashboard", url: "https://buddychip.app/dashboard" },
            ],
            [
              { text: "📚 Help", callback_data: "show_help" },
              { text: "⚙️ Settings", callback_data: "show_settings" },
            ],
          ]
        : [
            [
              { text: "🔗 Link Account", callback_data: "link_account" },
              { text: "🌐 Create Account", url: "https://buddychip.app" },
            ],
            [
              { text: "📚 Help", callback_data: "show_help" },
              { text: "🎯 Try Demo", callback_data: "demo_mode" },
            ],
          ];

      await context.bot.sendMessage(chatId, welcomeMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });

      context.logger.info("Welcome message sent successfully", {
        chatId: chatId.toString(),
        userId: telegramUser.id.toString(),
        metadata: { command: "/start", isLinked },
      });
    } catch (error) {
      context.logger.error("Error in /start command", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error, context);
    }
  }
}
