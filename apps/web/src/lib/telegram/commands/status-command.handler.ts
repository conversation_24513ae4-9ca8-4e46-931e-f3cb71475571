/**
 * Status Command Handler
 * Handles the /status command for Telegram bot
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseCommandHandler } from "./base/command-handler.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { prisma } from "../../db-utils";

/**
 * <PERSON><PERSON> for the /status command
 * Provides account status, usage limits, and plan information
 */
export class Status<PERSON>ommandHandler extends BaseCommandHandler {
  constructor() {
    super("status", "Check account status, usage limits, and plan information");
  }

  async handle(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;

    if (!telegramUser) {
      await context.bot.sendMessage(
        chatId,
        "❌ Unable to identify user. Please try again."
      );
      return;
    }

    context.logger.logCommand("status", telegramUser.id.toString(), chatId.toString(), true);

    try {
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        // User not linked - show limited status
        const unlinkedStatusMessage = `
📊 *Account Status*

*Connection Status:* ❌ Not linked to BuddyChip account

*Current Access:*
• ⚠️ Limited Twitter URL processing
• ⚠️ Basic content creation
• ❌ Image generation (requires linking)
• ❌ Usage analytics
• ❌ Premium AI models

*To unlock full features:*
1. Link your BuddyChip account
2. Get access to premium features
3. View detailed usage analytics
4. Higher usage limits

*Telegram Account Info:*
• Telegram ID: \`${dbTelegramUser.telegramId}\`
• Username: ${telegramUser.username ? `@${telegramUser.username}` : "Not set"}
• Active since: ${dbTelegramUser.createdAt.toLocaleDateString()}
• Last active: ${dbTelegramUser.lastActiveAt?.toLocaleDateString() || "Now"}

Link your account to see detailed usage statistics and unlock premium features!
        `;

        const keyboard = [
          [{ text: "🔗 Link Account", callback_data: "link_account" }],
          [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
          [{ text: "🎯 Try Demo", callback_data: "demo_mode" }],
        ];

        await context.bot.sendMessage(chatId, unlinkedStatusMessage, {
          parse_mode: "Markdown",
          reply_markup: { inline_keyboard: keyboard },
        });

        context.logger.info("Unlinked status message sent", {
          chatId: chatId.toString(),
          userId: telegramUser.id.toString(),
          metadata: { command: "/status", isLinked: false },
        });
        return;
      }

      // User is linked - get full status from database
      const user = await prisma.user.findUnique({
        where: { id: dbTelegramUser.userId },
        include: {
          plan: {
            include: {
              features: true,
            },
          },
        },
      });

      if (!user) {
        await context.bot.sendMessage(
          chatId,
          "❌ Error: Linked account not found. Please contact support."
        );
        return;
      }

      // Get usage limits from plan features
      const aiCallsFeature = user.plan.features.find((f) => f.name === "ai_calls");
      const imageGenFeature = user.plan.features.find((f) => f.name === "image_generation");

      const aiCallsLimit = aiCallsFeature?.limit ?? 0;
      const imageGenLimit = imageGenFeature?.limit ?? 0;

      // Get session statistics
      const sessionStats = await context.sessionService.getSessionStats();
      const userStats = await context.userService.getUserStats();

      const statusMessage = `
📊 *Account Status*

*Plan Information:*
• Plan: ${user.plan.displayName}
• Status: ${user.plan.isActive ? "✅ Active" : "❌ Inactive"}
• Member since: ${user.createdAt.toLocaleDateString()}

*Usage Limits:*
• AI Calls: ${aiCallsLimit === -1 ? "Unlimited" : `${aiCallsLimit}/month`}
• Image Generation: ${imageGenLimit === -1 ? "Unlimited" : `${imageGenLimit}/month`}

*Account Activity:*
• Last active: ${user.lastActiveAt?.toLocaleDateString() || "Never"}
• Telegram linked: ${dbTelegramUser.createdAt.toLocaleDateString()}
• Active sessions: ${sessionStats.totalActive}

*Features Available:*
• ✅ Twitter URL processing
• ✅ Content creation (/create)
• ✅ Image generation (/image)
• ✅ Full conversation history
• ✅ Premium AI models
• ✅ Enhanced responses
• ✅ Priority support

*Security & Privacy:*
• Account secure: ✅
• Data encrypted: ✅
• Auto cleanup: ✅

Visit your dashboard for detailed usage analytics and billing information.
      `;

      const keyboard = [
        [
          {
            text: "🌐 Open Dashboard",
            url: "https://buddychip.app/dashboard",
          },
          { text: "📈 Usage Details", callback_data: "usage_details" },
        ],
        [
          { text: "🔄 Refresh", callback_data: "refresh_status" },
          { text: "⚙️ Settings", callback_data: "show_settings" },
        ],
      ];

      await context.bot.sendMessage(chatId, statusMessage, {
        parse_mode: "Markdown",
        reply_markup: { inline_keyboard: keyboard },
      });

      context.logger.info("Status message sent successfully", {
        chatId: chatId.toString(),
        userId: telegramUser.id.toString(),
        metadata: { 
          command: "/status", 
          isLinked: true,
          planName: user.plan.displayName,
          planActive: user.plan.isActive,
        },
      });
    } catch (error) {
      context.logger.error("Error in /status command", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });

      await this.sendError(chatId, error, context);
    }
  }
}
