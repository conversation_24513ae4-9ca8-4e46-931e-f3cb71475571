/**
 * Telegram Bot Core
 * Core bot class that manages the bot instance and basic webhook handling
 */

import TelegramBot from "node-telegram-bot-api";
import type { TelegramHandlerContext } from "./telegram-handler-context";
import { TelegramSessionService } from "../services/telegram-session.service";
import { TelegramUserService } from "../services/telegram-user.service";
import { TelegramSecurityService } from "../services/telegram-security.service";
import { TelegramLogger } from "../utils/telegram-logger";
import { createTelegramHandlerContext } from "./telegram-handler-context";

export interface TelegramBotConfig {
  token: string;
  enablePolling?: boolean;
  webhookUrl?: string;
  webhookSecret?: string;
  pollingOptions?: TelegramBot.PollingOptions;
  webhookOptions?: TelegramBot.WebHookOptions;
}

/**
 * Core Telegram bot class
 * Manages bot instance, webhook setup, and provides context for handlers
 */
export class TelegramBotCore {
  private bot: TelegramBot;
  private context: TelegramHandlerContext;
  private isInitialized = false;
  private updateHandlers: Array<(update: any) => Promise<void>> = [];

  constructor(private config: TelegramBotConfig) {
    // Initialize bot instance
    this.bot = new TelegramBot(config.token, {
      polling: false, // We'll handle polling/webhooks manually
    });

    // Initialize services
    const logger = new TelegramLogger();
    const sessionService = new TelegramSessionService(logger);
    const userService = new TelegramUserService(logger);
    const securityService = new TelegramSecurityService(logger);

    // Create handler context
    this.context = createTelegramHandlerContext(
      this.bot,
      sessionService,
      userService,
      securityService,
      logger,
      {
        token: config.token,
        enablePolling: config.enablePolling || false,
        webhookUrl: config.webhookUrl,
      }
    );

    this.context.logger.info("TelegramBotCore initialized", {
      metadata: {
        enablePolling: config.enablePolling,
        hasWebhookUrl: !!config.webhookUrl,
        hasWebhookSecret: !!config.webhookSecret,
      },
    });
  }

  /**
   * Initialize the bot (setup polling or webhooks)
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      this.context.logger.warn("Bot already initialized");
      return;
    }

    try {
      // Get bot info
      const botInfo = await this.bot.getMe();
      this.context.logger.info("Bot information retrieved", {
        metadata: {
          botId: botInfo.id,
          botUsername: botInfo.username,
          botFirstName: botInfo.first_name,
        },
      });

      // Setup polling or webhooks
      if (this.config.enablePolling) {
        await this.setupPolling();
      } else if (this.config.webhookUrl) {
        await this.setupWebhook();
      } else {
        throw new Error("Either polling must be enabled or webhook URL must be provided");
      }

      this.isInitialized = true;
      this.context.logger.info("TelegramBotCore initialization completed");
    } catch (error) {
      this.context.logger.error("Failed to initialize TelegramBotCore", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      throw error;
    }
  }

  /**
   * Setup polling for development
   */
  private async setupPolling(): Promise<void> {
    this.context.logger.info("Setting up polling for development");

    const pollingOptions: TelegramBot.PollingOptions = {
      interval: 1000,
      autoStart: false,
      params: {
        timeout: 10,
      },
      ...this.config.pollingOptions,
    };

    // Set up update handler
    this.bot.on("polling_error", (error) => {
      this.context.logger.error("Polling error", {
        metadata: { error: error.message },
      });
    });

    // Handle all updates through our custom handler
    this.bot.on("message", async (message) => {
      await this.handleUpdate({ message });
    });

    this.bot.on("callback_query", async (callbackQuery) => {
      await this.handleUpdate({ callback_query: callbackQuery });
    });

    // Start polling
    await this.bot.startPolling(pollingOptions);
    this.context.logger.info("Polling started successfully");
  }

  /**
   * Setup webhook for production
   */
  private async setupWebhook(): Promise<void> {
    if (!this.config.webhookUrl) {
      throw new Error("Webhook URL is required for webhook setup");
    }

    this.context.logger.info("Setting up webhook for production", {
      metadata: { webhookUrl: this.config.webhookUrl },
    });

    const webhookOptions: TelegramBot.WebHookOptions = {
      port: process.env.PORT ? parseInt(process.env.PORT) : 3000,
      ...this.config.webhookOptions,
    };

    if (this.config.webhookSecret) {
      webhookOptions.secret_token = this.config.webhookSecret;
    }

    // Set webhook
    await this.bot.setWebHook(this.config.webhookUrl, webhookOptions);
    this.context.logger.info("Webhook set successfully");

    // Verify webhook
    const webhookInfo = await this.bot.getWebHookInfo();
    this.context.logger.info("Webhook info", {
      metadata: {
        url: webhookInfo.url,
        hasCustomCertificate: webhookInfo.has_custom_certificate,
        pendingUpdateCount: webhookInfo.pending_update_count,
        lastErrorDate: webhookInfo.last_error_date,
        lastErrorMessage: webhookInfo.last_error_message,
      },
    });
  }

  /**
   * Handle incoming updates
   */
  private async handleUpdate(update: any): Promise<void> {
    try {
      this.context.logger.debug("Processing update", {
        metadata: {
          updateId: update.update_id,
          hasMessage: !!update.message,
          hasCallbackQuery: !!update.callback_query,
          hasInlineQuery: !!update.inline_query,
        },
      });

      // Call all registered update handlers
      for (const handler of this.updateHandlers) {
        try {
          await handler(update);
        } catch (error) {
          this.context.logger.error("Update handler error", {
            metadata: {
              error: error instanceof Error ? error.message : String(error),
              updateId: update.update_id,
            },
          });
        }
      }
    } catch (error) {
      this.context.logger.error("Error handling update", {
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          updateId: update.update_id,
        },
      });
    }
  }

  /**
   * Process webhook update (for production)
   */
  async processWebhookUpdate(update: any): Promise<void> {
    await this.handleUpdate(update);
  }

  /**
   * Register an update handler
   */
  onUpdate(handler: (update: any) => Promise<void>): void {
    this.updateHandlers.push(handler);
    this.context.logger.debug("Update handler registered", {
      metadata: { totalHandlers: this.updateHandlers.length },
    });
  }

  /**
   * Get the handler context
   */
  getContext(): TelegramHandlerContext {
    return this.context;
  }

  /**
   * Get the bot instance
   */
  getBot(): TelegramBot {
    return this.bot;
  }

  /**
   * Check if bot is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get bot statistics
   */
  async getStats(): Promise<{
    botInfo: TelegramBot.User;
    webhookInfo?: TelegramBot.WebhookInfo;
    isPolling: boolean;
    updateHandlerCount: number;
  }> {
    const botInfo = await this.bot.getMe();
    const stats = {
      botInfo,
      isPolling: this.config.enablePolling || false,
      updateHandlerCount: this.updateHandlers.length,
    };

    if (!this.config.enablePolling && this.config.webhookUrl) {
      const webhookInfo = await this.bot.getWebHookInfo();
      return { ...stats, webhookInfo };
    }

    return stats;
  }

  /**
   * Shutdown the bot gracefully
   */
  async shutdown(): Promise<void> {
    this.context.logger.info("Shutting down TelegramBotCore");

    try {
      if (this.config.enablePolling) {
        await this.bot.stopPolling();
        this.context.logger.info("Polling stopped");
      } else if (this.config.webhookUrl) {
        await this.bot.deleteWebHook();
        this.context.logger.info("Webhook deleted");
      }

      // Close bot instance
      await this.bot.close();
      this.isInitialized = false;
      this.context.logger.info("TelegramBotCore shutdown completed");
    } catch (error) {
      this.context.logger.error("Error during shutdown", {
        metadata: { error: error instanceof Error ? error.message : String(error) },
      });
      throw error;
    }
  }
}
