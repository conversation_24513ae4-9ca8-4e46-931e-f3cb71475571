import type TelegramBot from "node-telegram-bot-api";
import type { TelegramSessionService } from "../../services/telegram-session.service";
import type { TelegramUserService } from "../../services/telegram-user.service";
import type { TelegramSecurityService } from "../../services/telegram-security.service";
import type { TelegramLogger } from "../../telegram-logger";

/**
 * Shared context passed to all Telegram handlers
 * Contains all dependencies needed for processing Telegram updates
 */
export interface TelegramHandlerContext {
  /** The Telegram bot instance for sending messages */
  bot: TelegramBot;
  
  /** Service for managing user sessions and callback data */
  sessionService: TelegramSessionService;
  
  /** Service for user management and account linking */
  userService: TelegramUserService;
  
  /** Service for spam detection and rate limiting */
  securityService: TelegramSecurityService;
  
  /** Structured logger for Telegram operations */
  logger: TelegramLogger;
  
  /** Bot configuration */
  config: {
    token: string;
    enablePolling: boolean;
    webhookUrl?: string;
  };
}

/**
 * Factory function to create a handler context
 */
export function createTelegramHandlerContext(
  bot: TelegramBot,
  sessionService: TelegramSessionService,
  userService: TelegramUserService,
  securityService: TelegramSecurityService,
  logger: TelegramLogger,
  config: TelegramHandlerContext['config']
): TelegramHandlerContext {
  return {
    bot,
    sessionService,
    userService,
    securityService,
    logger,
    config,
  };
}
