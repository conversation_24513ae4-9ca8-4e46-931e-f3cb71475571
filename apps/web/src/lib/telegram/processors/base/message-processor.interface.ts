import type Telegram<PERSON>ot from "node-telegram-bot-api";
import type { TelegramHandlerContext } from "../../core/telegram-handler-context";

/**
 * Interface for message processors
 * Handles different types of messages (Twitter URLs, general text, etc.)
 */
export interface TelegramMessageProcessor {
  /**
   * Check if this processor can handle the given message
   * @param message - The Telegram message to check
   * @returns True if this processor can handle the message
   */
  canHandle(message: TelegramBot.Message): boolean;
  
  /**
   * Process the message
   * @param message - The Telegram message to process
   * @param context - Shared context with services and dependencies
   */
  process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;
  
  /**
   * Get the priority of this processor (higher = processed first)
   * @returns Priority number (0-100, where 100 is highest priority)
   */
  getPriority(): number;
  
  /**
   * Get a description of what this processor handles
   * @returns Human-readable description
   */
  getDescription(): string;
}

/**
 * Abstract base class for message processors
 * Provides common functionality and validation
 */
export abstract class BaseMessageProcessor implements TelegramMessageProcessor {
  constructor(
    protected readonly priority: number,
    protected readonly description: string
  ) {}

  abstract canHandle(message: TelegramBot.Message): boolean;
  abstract process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void>;

  getPriority(): number {
    return this.priority;
  }

  getDescription(): string {
    return this.description;
  }

  /**
   * Validate that the message has required properties
   * @param message - The Telegram message
   * @param context - Handler context
   * @returns True if message is valid
   */
  protected async validateMessage(
    message: TelegramBot.Message,
    context: TelegramHandlerContext
  ): Promise<boolean> {
    if (!message.from) {
      context.logger.warn("Message received without sender information");
      return false;
    }

    if (!message.text?.trim()) {
      context.logger.warn("Message received without text content");
      return false;
    }

    return true;
  }

  /**
   * Send a typing indicator to show the bot is processing
   * @param chatId - The chat ID
   * @param context - Handler context
   */
  protected async sendTypingIndicator(
    chatId: number,
    context: TelegramHandlerContext
  ): Promise<void> {
    try {
      await context.bot.sendChatAction(chatId, "typing");
    } catch (error) {
      context.logger.warn("Failed to send typing indicator", { error });
    }
  }

  /**
   * Send an error message to the user
   * @param chatId - The chat ID to send the error to
   * @param error - The error message or Error object
   * @param context - Handler context
   */
  protected async sendError(
    chatId: number,
    error: string | Error,
    context: TelegramHandlerContext
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    context.logger.error(`Message processor error: ${errorMessage}`);
    
    await context.bot.sendMessage(
      chatId,
      "❌ Sorry, I encountered an error processing your message. Please try again."
    );
  }
}
