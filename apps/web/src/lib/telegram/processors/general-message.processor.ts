/**
 * General Message Processor
 * Handles general text messages for Telegram bot (AI conversation)
 */

import type TelegramBot from "node-telegram-bot-api";
import { BaseMessageProcessor } from "./base/message-processor.interface";
import type { TelegramHandlerContext } from "../core/telegram-handler-context";
import { sanitizeText } from "../utils/telegram-validator";
import { getTelegramBenjiForUser } from "../../telegram-benji-agent";
import { checkTelegramFeatureLimit, recordTelegramFeatureUsage } from "../../telegram-rate-limiting";
import { FeatureType } from "../../../../prisma/generated";
import { formatForTelegram } from "../utils/telegram-formatter";

/**
 * Processor for general text messages (AI conversation)
 * Handles non-command, non-Twitter URL messages
 */
export class GeneralMessageProcessor extends BaseMessageProcessor {
  constructor() {
    super(10, "Process general text messages with AI conversation"); // Low priority (after commands and Twitter URLs)
  }

  canHandle(message: TelegramBot.Message): boolean {
    if (!message.text) return false;
    
    // Don't handle commands
    if (message.text.startsWith("/")) return false;
    
    // Don't handle Twitter URLs
    const twitterUrlRegex = /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
    if (twitterUrlRegex.test(message.text)) return false;
    
    // Handle all other text messages
    return true;
  }

  async process(message: TelegramBot.Message, context: TelegramHandlerContext): Promise<void> {
    const chatId = message.chat.id;
    const telegramUser = message.from;
    const messageText = message.text;

    if (!telegramUser || !messageText) {
      context.logger.warn("General message processor: Missing user or text", {
        chatId: chatId.toString(),
      });
      return;
    }

    context.logger.logMessageProcessing(
      "general_message",
      telegramUser.id.toString(),
      chatId.toString(),
      true
    );

    try {
      context.logger.debug("Processing general message", {
        telegramUserId: telegramUser.id.toString(),
        chatId: chatId.toString(),
        metadata: {
          messageLength: messageText.length,
          username: telegramUser.username,
        },
      });

      // Sanitize message text
      const sanitizedText = sanitizeText(messageText);
      if (!sanitizedText) {
        context.logger.warn("Empty message after sanitization", {
          telegramUserId: telegramUser.id.toString(),
          chatId: chatId.toString(),
          metadata: { originalLength: messageText.length },
        });

        await context.bot.sendMessage(
          chatId,
          "⚠️ Your message cannot be processed. Please send a valid text message."
        );
        return;
      }

      // Get or create user
      const dbTelegramUser = await context.userService.getOrCreateTelegramUser(telegramUser);

      if (!dbTelegramUser.userId) {
        await this.sendAccountLinkingMessage(chatId, context);
        return;
      }

      // Check subscription feature limits
      const featureLimit = await checkTelegramFeatureLimit(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1
      );

      if (!featureLimit.allowed) {
        await context.bot.sendMessage(
          chatId,
          `⚠️ AI calls limit exceeded. ${featureLimit.remaining} remaining this month.`
        );
        return;
      }

      // Send "typing" indicator
      await this.sendTypingIndicator(chatId, context);

      // Get Telegram Benji agent for the user
      const benji = await getTelegramBenjiForUser(
        dbTelegramUser.userId,
        dbTelegramUser.id,
        chatId.toString()
      );

      // Generate AI response optimized for Telegram
      const result = await benji.generateTelegramResponse(sanitizedText, {
        telegramUserId: dbTelegramUser.id,
        telegramChatId: chatId.toString(),
      });

      // Convert streaming result to text
      let responseText = "";
      for await (const chunk of result.textStream) {
        responseText += chunk;
      }

      context.logger.debug("Generated general message response", {
        telegramUserId: dbTelegramUser.id,
        metadata: {
          inputLength: sanitizedText.length,
          responseLength: responseText.length,
          responsePreview: responseText.substring(0, 100) + "...",
          hasContent: !!responseText.trim(),
        },
      });

      // Record usage
      await recordTelegramFeatureUsage(
        dbTelegramUser.userId,
        FeatureType.AI_CALLS,
        1,
        {
          messageType: "telegram-conversation",
          telegramUserId: dbTelegramUser.telegramId,
          chatId: chatId.toString(),
          messageLength: sanitizedText.length,
        }
      );

      if (responseText.trim()) {
        // Split long messages for Telegram's 4096 character limit
        const messages = formatForTelegram(responseText);

        for (const messageChunk of messages) {
          await context.bot.sendMessage(chatId, messageChunk, {
            parse_mode: "Markdown",
            disable_web_page_preview: true,
          });
        }

        // Add conversation to session history
        await context.sessionService.addMessageToHistory(
          dbTelegramUser.id,
          "user",
          sanitizedText
        );

        await context.sessionService.addMessageToHistory(
          dbTelegramUser.id,
          "assistant",
          responseText
        );

        context.logger.info("General message processing completed successfully", {
          chatId: chatId.toString(),
          userId: telegramUser.id.toString(),
          metadata: { 
            inputLength: sanitizedText.length,
            responseLength: responseText.length,
            messageChunks: messages.length,
          },
        });
      } else {
        await context.bot.sendMessage(
          chatId,
          "❌ Sorry, I couldn't generate a response right now. Please try again."
        );

        context.logger.warn("Empty response generated for general message", {
          telegramUserId: dbTelegramUser.id,
          metadata: { 
            input: sanitizedText.substring(0, 100),
            inputLength: sanitizedText.length,
          },
        });
      }
    } catch (error) {
      context.logger.error("Error processing general message", {
        chatId: chatId.toString(),
        telegramUserId: telegramUser.id.toString(),
        metadata: { 
          error: error instanceof Error ? error.message : String(error),
          messageLength: messageText.length,
        },
      });

      await this.sendError(chatId, error, context);
    }
  }

  /**
   * Send account linking message for general conversation
   */
  private async sendAccountLinkingMessage(chatId: number, context: TelegramHandlerContext): Promise<void> {
    const linkingMessage = `🔗 *Account Required for AI Conversation*

To have AI conversations and access advanced features, you need to link your BuddyChip account.

*Why link your account?*
• Access to premium AI models
• Conversation memory and context
• Higher usage limits
• Better response quality
• Personalized AI personality
• Priority support

*What you can do without linking:*
• Use basic commands (/help, /start)
• View information about the bot

Use /start to get started with account linking!`;

    const keyboard = [
      [{ text: "🔗 Link Account", callback_data: "link_account" }],
      [{ text: "🌐 Create Account", url: "https://buddychip.app" }],
      [
        { text: "📚 Help", callback_data: "show_help" },
        { text: "🎯 Try Demo", callback_data: "demo_mode" },
      ],
    ];

    await context.bot.sendMessage(chatId, linkingMessage, {
      parse_mode: "Markdown",
      reply_markup: { inline_keyboard: keyboard },
    });
  }
}
